import { DictionarieState } from '@/models/dictionarie';
import {
  create,
  index,
  remove,
  setAdditionalService,
  update,
} from '@/services/service';
import { updateAllServiceDuration } from '@/services/service-duration-calculator';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import {
  Button,
  Drawer,
  message,
  Modal,
  Popconfirm,
  Space,
  Switch,
  Typography,
} from 'antd';
import React, { useRef, useState } from 'react';
import AdditionalService from './AdditionalService';
import EditAdditionalModal from './EditAdditionalModal';
import EditModal from './EditModal';
import ServiceDurationModal from './ServiceDurationModal';

const Service: React.FC<{ dictionarie: DictionarieState }> = ({
  dictionarie,
}) => {
  const actionRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [current, setCurrent] = useState<API.Service | undefined>(undefined);

  // 是否显示增项服务配置弹窗
  const [showAdditional, setShowAdditional] = useState<boolean>(false);
  // 是否显示当前服务的增项服务关联弹窗
  const [showCurrentAdditional, setShowCurrentAdditional] =
    useState<boolean>(false);
  // 是否显示时长统计弹窗
  const [durationModalVisible, setDurationModalVisible] =
    useState<boolean>(false);

  const petSizeList =
    dictionarie?.list?.filter((item) => item.type === '宠物体型') || [];
  const typeValueEnum: { [key: string]: string } = {};
  petSizeList.forEach((item) => {
    typeValueEnum[item.code] = item.name;
  });

  const handleSave = async (values: API.Service) => {
    let response;
    const { id, ...info } = values;
    if (id) {
      response = await update(id, {
        ...info,
        size: info.size || null,
        hairType: info.hairType || null,
      });
    } else {
      response = await create(values);
    }

    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('操作成功');
      actionRef?.current?.reload();
      setModalVisible(false);
    }
  };

  const handleDel = async (record: API.Service) => {
    const { id } = record;
    const response = await remove(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('删除成功');
      actionRef?.current?.reload();
    }
  };

  const handleToggleStatus = async (record: API.Service) => {
    if (record.published) {
      handleSave({ ...record, published: false });
      return;
    }
    handleSave({ ...record, published: true });
  };

  const handleUpdateServiceDuration = async () => {
    try {
      const response = await updateAllServiceDuration();
      if (response.errCode) {
        message.error(response.msg || '更新失败');
      } else {
        message.success(
          `更新成功，共更新了 ${
            response.data?.updatedCount || 0
          } 个服务的平均时长`,
        );
        actionRef?.current?.reload();
      }
    } catch (error) {
      message.error('更新失败，请稍后重试');
    }
  };

  const columns: ProColumns<API.Service, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      hidden: true,
      hideInSearch: true,
    },
    {
      title: '服务名称',
      dataIndex: 'serviceName',
      width: 100,
      fixed: 'left',
    },
    {
      title: 'logo',
      dataIndex: 'logo',
      width: 50,
      hideInSearch: true,
      valueType: 'image',
    },
    {
      title: '服务品牌',
      dataIndex: ['serviceType', 'name'],
      width: 100,
      hideInSearch: true,
    },
    {
      title: '基础价格',
      dataIndex: 'basePrice',
      width: 100,
      hideInSearch: true,
      valueType: 'money',
    },
    {
      title: '平均服务时长',
      dataIndex: 'avgDuration',
      width: 100,
      hideInSearch: true,
      render: (_, record) => {
        if (record.avgDuration) {
          const hours = Math.floor(record.avgDuration / 60);
          const minutes = record.avgDuration % 60;
          if (hours > 0) {
            return `${hours}小时${minutes > 0 ? `${minutes}分钟` : ''}`;
          }
          return `${minutes}分钟`;
        }
        return '-';
      },
    },
    {
      title: '宠物类型',
      dataIndex: 'petTypes',
      width: 50,
      hideInSearch: true,
      render: (_, record) => {
        const types = record.petTypes.split(',');
        return types.map((type) => (type === 'cat' ? '猫' : '狗')).join('、');
      },
    },
    {
      title: '适用体型',
      dataIndex: 'size',
      width: 60,
      hideInSearch: true,
      valueEnum: typeValueEnum,
    },
    {
      title: '毛发类型',
      dataIndex: 'hairType',
      width: 60,
      hideInSearch: true,
      valueEnum: {
        short: '短毛',
        long: '长毛',
      },
    },
    {
      title: '按距离计费',
      dataIndex: 'distanceChargeFlag',
      width: 50,
      hideInSearch: true,
      valueType: 'switch',
    },
    {
      title: '支持权益卡',
      dataIndex: 'cardDiscountFlag',
      width: 50,
      hideInSearch: true,
      valueType: 'switch',
    },
    {
      title: '排序值',
      dataIndex: 'orderIndex',
      hidden: true,
      hideInSearch: true,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 60,
      hideInSearch: true,
      align: 'center',
      render: (_, entity) => {
        return (
          <Button
            type="link"
            onClick={() => {
              Modal.info({
                title: '描述',
                icon: null,
                width: 600,
                content: (
                  <div
                    style={{
                      width: '100%',
                      height: '100%',
                      overflow: 'auto',
                    }}
                    dangerouslySetInnerHTML={{
                      __html: entity.description || '',
                    }}
                  ></div>
                ),
              });
            }}
          >
            查看
          </Button>
        );
      },
    },
    {
      title: '增项服务',
      dataIndex: 'additionalService',
      width: 200,
      hideInSearch: true,
      align: 'center',
      render: (_, entity) => {
        return (
          <>
            <Typography.Text
              ellipsis={{ tooltip: true }}
              style={{ maxWidth: '200px' }}
            >
              {entity.additionalServices?.map((item) => item.name).join('、')}
            </Typography.Text>
            <Button
              type="link"
              onClick={() => {
                setCurrent(entity);
                setShowCurrentAdditional(true);
              }}
            >
              管理
            </Button>
          </>
        );
      },
    },
    {
      title: '启用状态',
      dataIndex: 'isEnabled',
      key: 'isEnabled',
      width: 100,
      align: 'center',
      hideInSearch: true,
      render: (_, record) => (
        <Switch
          checked={record.published}
          onChange={() => handleToggleStatus(record)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      ),
      valueEnum: {
        true: { text: '启用', status: 'Success' },
        false: { text: '禁用', status: 'Default' },
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      valueType: 'option',
      align: 'center',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setDurationModalVisible(true);
            }}
          >
            时长统计
          </Button>
          <Popconfirm
            title="确认删除？"
            onConfirm={() => {
              handleDel(record);
            }}
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.Service>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        request={async (params) => {
          const { errCode, msg, data } = await index({
            ...params,
          });
          if (errCode) {
            message.error(msg || '列表查询失败');
            return {
              data: [],
              total: 0,
            };
          }
          return {
            data: data?.list || [],
            total: data?.total || 0,
          };
        }}
        scroll={{ x: '100%' }}
        toolBarRender={() => [
          <Button key="updateDuration" onClick={handleUpdateServiceDuration}>
            更新服务平均时长
          </Button>,
          <Button
            key="editType"
            onClick={() => {
              setShowAdditional(true);
            }}
          >
            管理增项服务
          </Button>,
          <Button
            key="add"
            type="primary"
            onClick={() => {
              setCurrent(undefined);
              setModalVisible(true);
            }}
          >
            新增
          </Button>,
        ]}
      />
      <EditModal
        open={modalVisible}
        info={current}
        onClose={() => {
          setCurrent(undefined);
          setModalVisible(false);
        }}
        onSave={handleSave}
      />
      <Drawer
        title="管理增项服务"
        placement="right"
        closable={false}
        width={1000}
        onClose={() => {
          setShowAdditional(false);
        }}
        open={showAdditional}
      >
        <AdditionalService />
      </Drawer>
      <EditAdditionalModal
        open={showCurrentAdditional}
        service={current}
        list={current?.additionalServices?.map((a) => a.id) || []}
        onClose={() => {
          setShowCurrentAdditional(false);
        }}
        onSave={async (additional_service_ids: number[]) => {
          if (current) {
            await setAdditionalService(current.id, { additional_service_ids });
            actionRef?.current?.reload();
          }
        }}
      />
      <ServiceDurationModal
        open={durationModalVisible}
        service={current}
        onClose={() => {
          setDurationModalVisible(false);
          setCurrent(undefined);
        }}
      />
    </>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(Service);
